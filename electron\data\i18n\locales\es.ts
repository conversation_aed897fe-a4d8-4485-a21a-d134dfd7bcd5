/**
 * Spanish language pack
 */
export default {
  // Error messages
  errors: {
    success: "Éxito",
    deviceNotConnected: "Dispositivo no conectado",
    invalidParam: "Parámetro inválido",
    operateFailed: "Operación fallida",
    noData: "Sin datos",
    internalError: "Error interno",
    connectionExists: "La conexión ya existe",
    fileContentEmpty: "El contenido del archivo está vacío",
    deviceNotConnectedOrDisconnected:
      "Dispositivo no conectado o desconectado.",
    getServiceErrorInfo:
      "No se pudo obtener la información de error correspondiente",
    saveReportFileError: "Error al guardar el archivo de informe rpt",
    getConfigureListError: "Error al obtener la lista de configuración",
    loadConfigureError: "Error al cargar la configuración",
    cancelUploadError: "Error al cancelar la carga del archivo de onda",
    openWaveFileError: "Error al abrir el archivo de onda",
    getFileDataSuccess: "¡Contenido de datos del archivo recuperado con éxito!",
  },

  // Log messages
  logs: {
    reportController: {
      getCommonReportListEntry: "getCommonReportList parámetros de entrada",
      getCommonReportListReturn: "getCommonReportList retorno",
      getCommonReportListError: "getCommonReportList error",
      cancelUploadStart: "Cancelar carga de archivo de onda iniciada",
      cancelUploadError: "Error al cancelar carga de archivo de onda",
      openWaveFileStart: "Abrir archivo de onda iniciado",
      openWaveFileError: "Error al abrir archivo de onda",
    },
    configureService: {
      getConfigureListError: "Error al obtener lista de configuración",
      loadConfigureError: "Error al cargar configuración",
    },
    paramService: {
      getDiffParamComplete:
        "Comparación completada, número de grupos de diferencias",
      getAllDiffParamError: "getAllDiffParam error",
    },
  },

  // Common messages
  common: {
    start: "Iniciado",
    stop: "Detenido",
    other: "Otro",
    loading: "Cargando...",
    success: "Éxito",
    failed: "Fallido",
    cancel: "Cancelar",
    confirm: "Confirmar",
  },

  // Dialog related
  dialogs: {
    customTitle: "Título personalizado",
    customMessage: "Personalizar contenido del mensaje",
    additionalInfo: "Otra información adicional",
    messageBoxOpened: "Se abrió el cuadro de mensaje",
    confirmButtonClicked: "hacer clic en el botón de confirmar",
    cancelButtonClicked: "hacer clic en el botón de cancelar",
    fileSelection: "Selección de archivo",
    fileSave: "Guardar archivo",
    selectPic: "seleccionar imagen",
    images: "Imágenes",
    files: "Archivos",
    textFiles: "Archivos de texto",
    all: "Todo",
  },

  // System folders
  systemFolders: {
    desktop: "Escritorio",
    documents: "Documentos",
    downloads: "Descargas",
    music: "Música",
    pictures: "Imágenes",
    videos: "Videos",
  },

  // Device operations
  deviceOperations: {
    deviceIpPortExists: "El puerto IP del dispositivo ya existe",
    addCompleted: "Agregar completado",
    addFailed: "Error al agregar",
    updateCompleted: "Actualización completada",
    updateFailed: "Error de actualización",
  },

  // Configuration related
  configuration: {
    configListNotExists: "La lista de configuración no existe",
    duplicateName: "Nombre duplicado, vuelva a ingresar",
    pathNotExists:
      "La ruta no existe, verifique si la configuración está configurada o guardada",
  },

  // File operations
  fileOperations: {
    sourceFolderNotExists: "La carpeta de origen no existe",
  },

  // Services related
  services: {
    // License service
    license: {
      cacheResult: "Usando resultado en caché",
      verificationTime: "Tiempo de verificación",
      verificationFailed: "Verificación fallida, tiempo",
      businessErrorInfo: "Obtener información de error comercial",
    },

    // Cross-platform service
    cross: {
      startGoService: "Iniciando servicio Go...",
      goServiceStartSuccess: "Servicio Go iniciado exitosamente, tiempo",
      goServiceStartFailed: "Fallo al iniciar servicio Go, tiempo",
      startPythonService: "Iniciando servicio Python...",
      pythonServiceStartSuccess:
        "Servicio Python iniciado exitosamente, tiempo",
      pythonServiceStartFailed: "Fallo al iniciar servicio Python, tiempo",
      optimizeStartParams: "Optimizar parámetros de inicio",
    },

    // Base service
    base: {
      getClientStart:
        "Comenzar a obtener cliente del dispositivo, ID del dispositivo",
      deviceNotFound:
        "Información del dispositivo no encontrada, ID del dispositivo",
      deviceNotConnected:
        "Dispositivo no conectado o cliente inválido, ID del dispositivo",
      getClientSuccess:
        "Cliente del dispositivo obtenido exitosamente, ID del dispositivo",
    },

    // Device connection service
    deviceConnect: {
      deviceConnected: "Dispositivo conectado, ID del dispositivo",
      callInterface: "Llamar interfaz",
      useDeviceId: "Usar ID del dispositivo como clave",
      notifyFrontend:
        "Notificar al frontend para modificar el estado de conexión",
      connectionAttempt: "intento de conexión fallido",
      cacheConnection: "Cachear objeto de conexión exitoso",
      resetData: "Restablecer datos",
      addDebugInfo:
        "Agregar debugInfo y debugItemMap a realSingleGlobalDeviceInfo",
      deleteConnection: "Eliminar objeto de conexión",
    },

    // Device info service
    deviceInfo: {
      excelExport: "Exportación Excel",
      getDeviceInfoAsync:
        "Obtener información del dispositivo de forma asíncrona, devolver lista DeviceinfoItem",
      getDeviceInfoStart: "Obtener información del dispositivo",
      businessErrorInfo: "Obtener información de error comercial",
      exportDeviceInfoBasic: "Exportar información básica del dispositivo",
      exportStart: "Comenzar exportación",
      defineColumns: "Definir columnas",
      exportSuccess: "Exportación exitosa",
    },

    // Database service
    database: {
      sqliteStorage: "Almacenamiento de datos SQLite",
      initialize: "Inicializar",
      initializeTable: "Inicializar tabla",
      addTestData: "Agregar datos de prueba (sqlite)",
      deleteTestData: "Eliminar datos de prueba (sqlite)",
      updateTestData: "Actualizar datos de prueba (sqlite)",
      queryTestData: "Consultar datos de prueba (sqlite)",
    },

    // Configure service
    configure: {
      getConfigureList: "Obtener lista de configuración",
      addConfigure: "Agregar configuración",
      setId: "Establecer ID",
      projectNotExists: "El proyecto no existe",
      duplicateName: "Nombre duplicado, vuelva a ingresar",
      addConfigureException: "Excepción al agregar configuración",
      projectNotFound: "Proyecto no encontrado",
      projectPathNotFound: "Ruta del proyecto no encontrada",
      replaceWithNew: "Reemplazar con nuevo contenido",
      projectNotFoundShort: "Proyecto no encontrado",
      operationTypeIncorrect:
        "Tipo de operación incorrecto, valores permitidos [project,hmi]",
      renameConfigureException: "Excepción al renombrar configuración",
      getConfigureListException: "Excepción al obtener lista de configuración",
      configureSaveException: "Excepción al guardar configuración",
      openConfigureFolder: "Abrir carpeta de configuración",
      loadConfigureException: "Excepción al cargar configuración",
    },

    // More service
    more: {
      importPathNotExists: "La ruta del archivo de importación no existe",
      selectCorrectConfigFile:
        "Por favor seleccione el archivo de configuración correcto",
      importProjectConfigException:
        "Excepción al importar configuración del proyecto",
      exportPathNotExists: "La ruta de exportación no existe",
      exportProjectConfigException:
        "Excepción al exportar configuración del proyecto",
    },

    // Window service
    window: {
      initializeWindowState: "Inicializar gestión del estado de ventana",
      startPeriodicSave:
        "Iniciar guardado periódico del estado de ventana (cada 30 segundos)",
      windowStateInitialized: "Gestión del estado de ventana inicializada",
      windowStateInitFailed:
        "Fallo en la inicialización de la gestión del estado de ventana",
      debugAutoOpenDevTools:
        "Abrir automáticamente DevTools en modo debug, ahora comentado",
      clickNotification: "Hiciste clic en el mensaje de notificación",
      closeNotification: "Cerraste el mensaje de notificación",
      directExit: "Salida directa",
      minimizeToTray: "Minimizar a bandeja",
      dragWindow: "Arrastrar ventana",
      saveCurrentWindowState: "Guardar estado actual de ventana",
      windowStateSaved: "Estado de ventana guardado manualmente",
      saveWindowStateFailed: "Fallo al guardar estado de ventana manualmente",
      windowStateRestored: "Estado de ventana restaurado manualmente",
      restoreWindowStateFailed:
        "Fallo al restaurar estado de ventana manualmente",
      checkProgramExists: "Verificar si el programa existe",
    },

    // Tray service
    tray: {
      trayService: "Bandeja",
    },

    // Report service
    report: {
      reportService: "Servicio relacionado con informes",
      getCommonReport: "Obtener informe común",
      getCommonReportEntry:
        "Registro de entrada del método de obtener informe común",
      errorHandledInCatch: "Error manejado en catch",
      getGroupReport: "Obtener informe de grupo",
      getOperateReport: "Obtener informe de operación",
      getAuditReport: "Obtener informe de auditoría",
      exportCommonReport: "Exportar informe común",
      clearReport: "Limpiar informe",
      refreshReport: "Actualizar informe",
      uploadWaveFile: "Subir archivo de onda",
      cancelUploadWaveFile: "Cancelar subida de archivo de onda",
      openWaveFile: "Abrir archivo de onda",
      passFilePathAsParam: "Pasar ruta del archivo como parámetro al exe",
      generateExcelFile: "Generar archivo Excel",
      tableHeader: "Encabezado de tabla",
      exportContentFields: "Campos de contenido de exportación",
    },
  },
};
