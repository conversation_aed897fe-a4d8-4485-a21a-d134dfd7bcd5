/**
 * 中文语言包
 */
export default {
  // 错误信息
  errors: {
    success: "成功",
    deviceNotConnected: "装置未连接",
    invalidParam: "参数错误",
    operateFailed: "操作失败",
    noData: "无数据",
    internalError: "内部错误",
    connectionExists: "连接已存在",
    fileContentEmpty: "文件内容为空",
    deviceNotConnectedOrDisconnected: "设备未连接或已断开。",
    getServiceErrorInfo: "未获取对应错误信息",
    saveReportFileError: "保存报告rpt文件出错",
    getConfigureListError: "获取组态列表异常",
    loadConfigureError: "加载组态异常",
    cancelUploadError: "取消录波文件上招异常",
    openWaveFileError: "打开录波文件异常",
    getFileDataSuccess: "获取文件数据内容成功！",
    getHistoryReportError: "获取历史报告错误",
    getSuccessful: "获取成功",
    errorHandledInCatch: "错误已在 catch 里处理",
    waveFileNotFound: "录波文件未找到",
    waveFileSizeZero: "录波文件大小为0",
    uploadException: "上传异常",
    uploadFinished: "上传结束",
    saveReportXlsxError: "保存报告xlsx文件出错",
    invalidXmlStructure: "Invalid XML structure: missing configVersion",
    failedToGetTreeMenu: "Failed to get tree menu",
    missingHeaders: "缺失表头",
    excelParseFailed: "Excel解析失败",
    paramModifyError: "定值项修改错误, 错误的条目是，",
    paramConfirmError: "定值确认错误, 错误原因是：",
    errorReason: "，错误原因是：",
    invalidValue: "值不合法",
    errorItem: "错误的条目：",
    description: ", 描述",
    excelFileParseError: "Excel文件解析失败：",
    csvFileParseError: "CSV文件解析失败：",
    xmlFileParseError: "XML文件解析失败：",
    connectionFailed: "连接失败",
    connectionTimeout: "连接超时",
    dataLengthMismatch: "装置返回vkeys数据长度与请求中长度不一致",
  },

  // 日志信息
  logs: {
    reportController: {
      getCommonReportListEntry: "getCommonReportList 入参",
      getCommonReportListReturn: "getCommonReportList 返回",
      getCommonReportListError: "getCommonReportList 异常",
      cancelUploadStart: "取消录波文件上招开始",
      cancelUploadError: "取消录波文件上招异常",
      openWaveFileStart: "打开录波文件开始",
      openWaveFileError: "打开录波文件异常",
    },
    configureService: {
      getConfigureListError: "获取组态列表异常",
      loadConfigureError: "加载组态异常",
    },
    paramService: {
      getDiffParamComplete: "比对完成，差异组数",
      getAllDiffParamError: "getAllDiffParam error",
      getParamInfoEntry: "getParamInfo 入参",
      getParamInfoReturn: "getParamInfo 返回",
      getParamInfoError: "getParamInfo 异常",
      startGetParamInfo: "开始获取参数定值，分页",
      getAllParamInfoStart: "开始获取所有参数定值",
      getAllParamInfoSuccess: "获取所有参数定值成功，总数",
      modifyParamStart: "开始修改参数定值",
      validateParam: "校验参数项",
      validateFailed: "参数校验失败",
      validatePassed: "参数校验通过，准备下发",
      setTimeout: "设置超时时间",
      sendResponse: "下发响应",
      modifySuccess: "修改成功",
      sendFailed: "下发失败",
      businessError: "业务错误",
      getAllDiffParamStart: "开始批量比对参数",
      excelParseFailed: "Excel解析失败",
      csvParseFailed: "CSV解析失败",
      xmlParseFailed: "XML解析失败",
      fileParseComplete: "文件解析完成",
      getDiffParamStart: "开始单组比对参数",
      diffComplete: "比对完成，差异项数",
      importParamStart: "开始导入参数定值",
      paramReady: "参数准备下发",
      importSuccess: "导入成功",
      exportAllParamStart: "开始导出所有参数定值",
      exportComplete: "导出完成",
      exportParamStart: "开始导出分组参数",
      getGroupItemsStart: "获取分组参数项",
      getParamValueFailed: "获取参数值失败",
      getGroupItemsComplete: "获取完成，参数项数",
      getAllGroupItemsStart: "获取所有分组参数项",
      groupParamCount: "分组：{group}，参数项数：{count}",
      getCurrentRunAreaStart: "获取当前运行区",
      getCurrentRunAreaSuccess: "获取成功",
      getCurrentRunAreaFailed: "获取失败",
      selectRunAreaStart: "选择定值区",
      runAreaEmpty: "定值区不能为空",
      selectRunAreaSuccess: "选择成功",
      selectRunAreaFailed: "选择失败",
    },
    debugInfoMenuService: {
      initialized: "初始化完成",
      getDebugInfoEntry: "getDebugInfo 入参",
      getTreeMenuError: "getTreeMenu 异常",
      getTreeMenuComplete: "处理完成，菜单数量",
    },
  },

  // 通用消息
  common: {
    start: "开始了",
    stop: "停止了",
    other: "其他",
    loading: "加载中...",
    success: "成功",
    failed: "失败",
    cancel: "取消",
    confirm: "确认",
    connectionSuccess: "连接成功",
    requestDataSize: "请求的数据大小",
    responseDataSize: "接口返回数据大小",
    exportMatrixSettings: "出口矩阵定值",
  },

  // 对话框相关
  dialogs: {
    customTitle: "自定义标题",
    customMessage: "自定义消息内容",
    additionalInfo: "其他附加信息",
    messageBoxOpened: "已打开消息框",
    confirmButtonClicked: "点击了确认按钮",
    cancelButtonClicked: "点击了取消按钮",
    fileSelection: "文件选择",
    fileSave: "文件保存",
    selectPic: "选择图片",
    images: "图片",
    files: "文件",
    textFiles: "文本文件",
    all: "全部",
  },

  // 系统文件夹
  systemFolders: {
    desktop: "桌面",
    documents: "文档",
    downloads: "下载",
    music: "音乐",
    pictures: "图片",
    videos: "视频",
  },

  // 设备操作相关
  deviceOperations: {
    deviceIpPortExists: "装置IP端口已存在",
    addCompleted: "新增完成",
    addFailed: "新增失败",
    updateCompleted: "修改完成",
    updateFailed: "修改失败",
  },

  // 配置相关
  configuration: {
    configListNotExists: "组态列表不存在",
    duplicateName: "名称重复，请重新输入",
    pathNotExists: "路径不存在，请检查组态是否配置或保存",
  },

  // 文件操作相关
  fileOperations: {
    sourceFolderNotExists: "源文件夹不存在",
  },

  // 托盘菜单
  tray: {
    show: "显示",
    exit: "退出",
  },

  // 设备信息
  deviceInfo: {
    basicInfo: "装置基本信息",
  },

  // 备份相关
  backup: {
    cancel: "取消",
    userCancel: "用户取消备份",
    userCancelShort: "用户取消",
    start: "开始",
    processing: "处理中",
    success: "成功",
    failed: "失败",
    complete: "完成",
    exception: "异常",
    exportParam: "导出定值",
    exportReport: "导出报告",
    exportWave: "导出录波文件",
    exportConfig: "导出配置文件",
    deviceParam: "装置定值",
    deviceFault: "装置故障信息",
    deviceWave: "装置录波文件",
    deviceConfig: "装置配置文件",
    paramExport: "定值导出.xlsx",
    xmlConfig: "XML配置",
    logFiles: "日志文件",
    logs: "日志",
    cancelUploadError: "取消上传任务异常",
  },

  // 设备文件相关
  deviceFile: {
    uploadSuccess: "上传成功",
    cancelSuccess: "取消成功",
    downloadSuccess: "下载成功",
    downloadFileList: "下载文件列表",
    headers: {
      index: "序号",
      fileName: "名称",
      fileSize: "文件大小",
      filePath: "文件路径",
      lastModified: "最后修改时间",
    },
  },

  // 变量相关
  variable: {
    registerFailed: "注册变量失败，失败原因：",
    modifyFailed: "修改变量失败，失败原因：",
    deleteFailed: "删除变量失败，失败原因：",
    modifyError: "变量修改失败",
    deleteError: "变量删除失败",
    debugVariables: "装置调试变量",
    headers: {
      index: "序号",
      name: "变量名称",
      description: "变量描述",
      type: "变量类型",
      value: "变量值",
    },
  },

  // 报告相关
  reports: {
    service: "报告相关Service",
    description: "负责历史报告、操作报告、故障报告的查询、导出、清除等业务逻辑",
    getCommonReport: "获取通用报告",
    getCommonReportEntry: "获取通用报告方法入口日志",
    getGroupReport: "获取整组报告",
    getOperateReport: "获取操作报告",
    getAuditReport: "获取审计报告",
    exportCommonReport: "导出通用报告",
    clearReport: "清除报告",
    refreshReport: "刷新报告",
    uploadWave: "录波文件上招",
    cancelUpload: "取消录波文件上招",
    openWaveFile: "打开录波文件",
    getOperateReportEnd: "获取操作报告结束",
    getAuditReportEnd: "获取审计报告结束",
    workbookName: "report",
    exportContent: "导出内容的字段",
    headers: {
      reportId: "报告编号",
      reportTime: "报告时间",
      description: "描述",
      name: "名称",
      time: "时间",
      operateAddress: "操作地址",
      operateParam: "操作参数",
      value: "值",
      step: "步骤",
      source: "源",
      sourceType: "源类型",
      result: "结果",
      module: "模块",
      message: "消息",
      type: "类型",
      level: "级别",
      origin: "来源",
      user: "用户",
    },
  },

  // 参数相关
  params: {
    headers: {
      index: "序号",
      name: "名称",
      description: "描述",
      value: "值",
      minValue: "最小值",
      maxValue: "最大值",
      step: "步长",
      unit: "单位",
    },
  },

  // 服务相关
  services: {
    // 许可证服务
    license: {
      cacheResult: "使用缓存结果",
      verificationTime: "验证耗时",
      verificationFailed: "验证失败，耗时",
      businessErrorInfo: "获取业务错误信息",
    },

    // 跨平台服务
    cross: {
      startGoService: "开始启动Go服务...",
      goServiceStartSuccess: "Go服务启动成功，耗时",
      goServiceStartFailed: "Go服务启动失败，耗时",
      startPythonService: "开始启动Python服务...",
      pythonServiceStartSuccess: "Python服务启动成功，耗时",
      pythonServiceStartFailed: "Python服务启动失败，耗时",
      optimizeStartParams: "优化启动参数",
    },

    // 基础服务
    base: {
      getClientStart: "开始获取设备客户端，设备ID",
      deviceNotFound: "未找到设备信息，设备ID",
      deviceNotConnected: "设备未连接或客户端无效，设备ID",
      getClientSuccess: "成功获取设备客户端，设备ID",
    },

    // 设备连接服务
    deviceConnect: {
      deviceConnected: "设备已连接，设备ID",
      callInterface: "调取接口",
      useDeviceId: "使用装置的id作为key",
      notifyFrontend: "通知前端修改连接状态",
      connectionAttempt: "次连接失败",
      cacheConnection: "将连接成功的连接对象缓存",
      resetData: "复位数据",
      addDebugInfo: "往realSingleGlobalDeviceInfo添加debugInfo 和debugItemMap",
      deleteConnection: "删除连接对象",
    },

    // 设备信息服务
    deviceInfo: {
      excelExport: "Excel导出",
      getDeviceInfoAsync: "异步获取装置信息，返回DeviceinfoItem列表",
      getDeviceInfoStart: "获取设备信息",
      businessErrorInfo: "获取业务错误信息",
      exportDeviceInfoBasic: "导出装置基本信息",
      exportStart: "开始导出",
      defineColumns: "定义列",
      exportSuccess: "导出成功",
    },

    // 数据库服务
    database: {
      sqliteStorage: "sqlite数据存储",
      initialize: "初始化",
      initializeTable: "初始化表",
      addTestData: "增 Test data (sqlite)",
      deleteTestData: "删 Test data (sqlite)",
      updateTestData: "改 Test data (sqlite)",
      queryTestData: "查 Test data (sqlite)",
    },

    // 组态服务
    configure: {
      getConfigureList: "获取组态列表",
      addConfigure: "新增组态",
      setId: "设置id",
      projectNotExists: "项目不存在",
      duplicateName: "名称重复，请重新输入",
      addConfigureException: "新增组态异常",
      projectNotFound: "未找到项目",
      projectPathNotFound: "未找到项目路径",
      replaceWithNew: "替换为新内容",
      projectNotFoundShort: "项目未找到",
      operationTypeIncorrect: "操作类型不正确，允许值[project,hmi]",
      renameConfigureException: "重命名组态异常",
      getConfigureListException: "获取组态列表异常",
      configureSaveException: "组态保存异常",
      openConfigureFolder: "打开组态文件夹",
      loadConfigureException: "加载组态异常",
    },

    // 更多服务
    more: {
      importPathNotExists: "待导入文件路径不存在",
      selectCorrectConfigFile: "请选择正确的配置文件",
      importProjectConfigException: "导入工程配置异常",
      exportPathNotExists: "导出路径不存在",
      exportProjectConfigException: "导出工程配置异常",
    },

    // 窗口服务
    window: {
      initializeWindowState: "初始化窗口状态管理",
      startPeriodicSave: "开始定时保存窗口状态（每30秒）",
      windowStateInitialized: "窗口状态管理已初始化",
      windowStateInitFailed: "窗口状态管理初始化失败",
      debugAutoOpenDevTools: "调试时自动打开DevTools，现已注释",
      clickNotification: "您点击了通知消息",
      closeNotification: "您关闭了通知消息",
      directExit: "直接退出",
      minimizeToTray: "最小化托盘",
      dragWindow: "拖拽窗口",
      saveCurrentWindowState: "保存当前窗口状态",
      windowStateSaved: "窗口状态已手动保存",
      saveWindowStateFailed: "手动保存窗口状态失败",
      windowStateRestored: "窗口状态已手动恢复",
      restoreWindowStateFailed: "手动恢复窗口状态失败",
      checkProgramExists: "检查程序是否存在",
    },

    // 托盘服务
    tray: {
      trayService: "托盘",
    },

    // 报告服务
    report: {
      reportService: "报告相关Service",
      getCommonReport: "获取通用报告",
      getCommonReportEntry: "获取通用报告方法入口日志",
      errorHandledInCatch: "错误已在 catch 里处理",
      getGroupReport: "获取整组报告",
      getOperateReport: "获取操作报告",
      getAuditReport: "获取审计报告",
      exportCommonReport: "导出通用报告",
      clearReport: "清除报告",
      refreshReport: "刷新报告",
      uploadWaveFile: "录波文件上招",
      cancelUploadWaveFile: "取消录波文件上招",
      openWaveFile: "打开录波文件",
      passFilePathAsParam: "将文件路径作为参数传递给exe",
      generateExcelFile: "生成Excel文件",
      tableHeader: "表格表头",
      exportContentFields: "导出内容的字段",
    },
  },
};
