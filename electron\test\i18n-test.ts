/**
 * 国际化功能测试
 * 验证service目录下的国际化修复是否正常工作
 */

import { t, setLanguage, getCurrentLanguage } from '../data/i18n/i18n';

// 测试函数
function testI18nFunctionality() {
  console.log('=== 国际化功能测试开始 ===');
  
  // 测试中文
  setLanguage('zh');
  console.log(`当前语言: ${getCurrentLanguage()}`);
  console.log(`许可证缓存结果: ${t('services.license.cacheResult')}`);
  console.log(`Go服务启动: ${t('services.cross.startGoService')}`);
  console.log(`设备连接: ${t('services.deviceConnect.deviceConnected')}`);
  console.log(`导出成功: ${t('services.deviceInfo.exportSuccess')}`);
  
  console.log('\n--- 切换到英文 ---');
  // 测试英文
  setLanguage('en');
  console.log(`Current Language: ${getCurrentLanguage()}`);
  console.log(`License Cache Result: ${t('services.license.cacheResult')}`);
  console.log(`Go Service Start: ${t('services.cross.startGoService')}`);
  console.log(`Device Connected: ${t('services.deviceConnect.deviceConnected')}`);
  console.log(`Export Success: ${t('services.deviceInfo.exportSuccess')}`);
  
  console.log('\n--- 切换到西班牙语 ---');
  // 测试西班牙语
  setLanguage('es');
  console.log(`Idioma Actual: ${getCurrentLanguage()}`);
  console.log(`Resultado Cache Licencia: ${t('services.license.cacheResult')}`);
  console.log(`Inicio Servicio Go: ${t('services.cross.startGoService')}`);
  console.log(`Dispositivo Conectado: ${t('services.deviceConnect.deviceConnected')}`);
  console.log(`Exportación Exitosa: ${t('services.deviceInfo.exportSuccess')}`);
  
  console.log('\n--- 切换到法语 ---');
  // 测试法语
  setLanguage('fr');
  console.log(`Langue Actuelle: ${getCurrentLanguage()}`);
  console.log(`Résultat Cache Licence: ${t('services.license.cacheResult')}`);
  console.log(`Démarrage Service Go: ${t('services.cross.startGoService')}`);
  console.log(`Appareil Connecté: ${t('services.deviceConnect.deviceConnected')}`);
  console.log(`Exportation Réussie: ${t('services.deviceInfo.exportSuccess')}`);
  
  // 测试不存在的键
  console.log('\n--- 测试不存在的键 ---');
  console.log(`不存在的键: ${t('services.nonexistent.key')}`);
  
  // 恢复中文
  setLanguage('zh');
  console.log('\n=== 国际化功能测试完成 ===');
}

// 导出测试函数
export { testI18nFunctionality };

// 如果直接运行此文件，执行测试
if (require.main === module) {
  testI18nFunctionality();
}
