/**
 * French language pack
 */
export default {
  // Error messages
  errors: {
    success: "Succès",
    deviceNotConnected: "Appareil non connecté",
    invalidParam: "Paramètre invalide",
    operateFailed: "Opération échouée",
    noData: "Aucune donnée",
    internalError: "Erreur interne",
    connectionExists: "La connexion existe déjà",
    fileContentEmpty: "Le contenu du fichier est vide",
    deviceNotConnectedOrDisconnected: "Appareil non connecté ou déconnecté.",
    getServiceErrorInfo:
      "Impossible d'obtenir les informations d'erreur correspondantes",
    saveReportFileError:
      "Erreur lors de la sauvegarde du fichier de rapport rpt",
    getConfigureListError:
      "Erreur lors de l'obtention de la liste de configuration",
    loadConfigureError: "Erreur lors du chargement de la configuration",
    cancelUploadError:
      "Erreur lors de l'annulation du téléchargement du fichier d'onde",
    openWaveFileError: "Erreur lors de l'ouverture du fichier d'onde",
    getFileDataSuccess: "Contenu des données du fichier récupéré avec succès !",
  },

  // Log messages
  logs: {
    reportController: {
      getCommonReportListEntry: "getCommonReportList paramètres d'entrée",
      getCommonReportListReturn: "getCommonReportList retour",
      getCommonReportListError: "getCommonReportList erreur",
      cancelUploadStart:
        "Annulation du téléchargement du fichier d'onde démarrée",
      cancelUploadError:
        "Erreur d'annulation du téléchargement du fichier d'onde",
      openWaveFileStart: "Ouverture du fichier d'onde démarrée",
      openWaveFileError: "Erreur d'ouverture du fichier d'onde",
    },
    configureService: {
      getConfigureListError: "Erreur d'obtention de la liste de configuration",
      loadConfigureError: "Erreur de chargement de la configuration",
    },
    paramService: {
      getDiffParamComplete:
        "Comparaison terminée, nombre de groupes de différences",
      getAllDiffParamError: "getAllDiffParam erreur",
    },
  },

  // Common messages
  common: {
    start: "Démarré",
    stop: "Arrêté",
    other: "Autre",
    loading: "Chargement...",
    success: "Succès",
    failed: "Échoué",
    cancel: "Annuler",
    confirm: "Confirmer",
  },

  // Dialog related
  dialogs: {
    customTitle: "Titre personnalisé",
    customMessage: "Personnaliser le contenu du message",
    additionalInfo: "Autres informations supplémentaires",
    messageBoxOpened: "Boîte de message ouverte",
    confirmButtonClicked: "cliquer sur le bouton de confirmation",
    cancelButtonClicked: "cliquer sur le bouton d'annulation",
    fileSelection: "Sélection de fichier",
    fileSave: "Enregistrer le fichier",
    selectPic: "sélectionner une image",
    images: "Images",
    files: "Fichiers",
    textFiles: "Fichiers texte",
    all: "Tout",
  },

  // System folders
  systemFolders: {
    desktop: "Bureau",
    documents: "Documents",
    downloads: "Téléchargements",
    music: "Musique",
    pictures: "Images",
    videos: "Vidéos",
  },

  // Device operations
  deviceOperations: {
    deviceIpPortExists: "Le port IP du périphérique existe déjà",
    addCompleted: "Ajout terminé",
    addFailed: "Échec de l'ajout",
    updateCompleted: "Mise à jour terminée",
    updateFailed: "Échec de la mise à jour",
  },

  // Configuration related
  configuration: {
    configListNotExists: "La liste de configuration n'existe pas",
    duplicateName: "Nom en double, veuillez ressaisir",
    pathNotExists:
      "Le chemin n'existe pas, veuillez vérifier si la configuration est configurée ou enregistrée",
  },

  // File operations
  fileOperations: {
    sourceFolderNotExists: "Le dossier source n'existe pas",
  },

  // Services related
  services: {
    // License service
    license: {
      cacheResult: "Utilisation du résultat en cache",
      verificationTime: "Temps de vérification",
      verificationFailed: "Vérification échouée, temps",
      businessErrorInfo: "Obtenir les informations d'erreur métier",
    },

    // Cross-platform service
    cross: {
      startGoService: "Démarrage du service Go...",
      goServiceStartSuccess: "Service Go démarré avec succès, temps",
      goServiceStartFailed: "Échec du démarrage du service Go, temps",
      startPythonService: "Démarrage du service Python...",
      pythonServiceStartSuccess: "Service Python démarré avec succès, temps",
      pythonServiceStartFailed: "Échec du démarrage du service Python, temps",
      optimizeStartParams: "Optimiser les paramètres de démarrage",
    },

    // Base service
    base: {
      getClientStart:
        "Commencer à obtenir le client de l'appareil, ID de l'appareil",
      deviceNotFound:
        "Informations de l'appareil non trouvées, ID de l'appareil",
      deviceNotConnected:
        "Appareil non connecté ou client invalide, ID de l'appareil",
      getClientSuccess:
        "Client de l'appareil obtenu avec succès, ID de l'appareil",
    },

    // Device connection service
    deviceConnect: {
      deviceConnected: "Appareil connecté, ID de l'appareil",
      callInterface: "Appeler l'interface",
      useDeviceId: "Utiliser l'ID de l'appareil comme clé",
      notifyFrontend: "Notifier le frontend pour modifier l'état de connexion",
      connectionAttempt: "tentative de connexion échouée",
      cacheConnection: "Mettre en cache l'objet de connexion réussi",
      resetData: "Réinitialiser les données",
      addDebugInfo:
        "Ajouter debugInfo et debugItemMap à realSingleGlobalDeviceInfo",
      deleteConnection: "Supprimer l'objet de connexion",
    },

    // Device info service
    deviceInfo: {
      excelExport: "Exportation Excel",
      getDeviceInfoAsync:
        "Obtenir les informations de l'appareil de manière asynchrone, retourner la liste DeviceinfoItem",
      getDeviceInfoStart: "Obtenir les informations de l'appareil",
      businessErrorInfo: "Obtenir les informations d'erreur métier",
      exportDeviceInfoBasic: "Exporter les informations de base de l'appareil",
      exportStart: "Commencer l'exportation",
      defineColumns: "Définir les colonnes",
      exportSuccess: "Exportation réussie",
    },

    // Database service
    database: {
      sqliteStorage: "Stockage de données SQLite",
      initialize: "Initialiser",
      initializeTable: "Initialiser la table",
      addTestData: "Ajouter des données de test (sqlite)",
      deleteTestData: "Supprimer des données de test (sqlite)",
      updateTestData: "Mettre à jour des données de test (sqlite)",
      queryTestData: "Interroger des données de test (sqlite)",
    },

    // Configure service
    configure: {
      getConfigureList: "Obtenir la liste de configuration",
      addConfigure: "Ajouter une configuration",
      setId: "Définir l'ID",
      projectNotExists: "Le projet n'existe pas",
      duplicateName: "Nom en double, veuillez ressaisir",
      addConfigureException: "Exception lors de l'ajout de configuration",
      projectNotFound: "Projet non trouvé",
      projectPathNotFound: "Chemin du projet non trouvé",
      replaceWithNew: "Remplacer par le nouveau contenu",
      projectNotFoundShort: "Projet non trouvé",
      operationTypeIncorrect:
        "Type d'opération incorrect, valeurs autorisées [project,hmi]",
      renameConfigureException: "Exception lors du renommage de configuration",
      getConfigureListException:
        "Exception lors de l'obtention de la liste de configuration",
      configureSaveException:
        "Exception lors de la sauvegarde de configuration",
      openConfigureFolder: "Ouvrir le dossier de configuration",
      loadConfigureException: "Exception lors du chargement de configuration",
    },

    // More service
    more: {
      importPathNotExists: "Le chemin du fichier d'importation n'existe pas",
      selectCorrectConfigFile:
        "Veuillez sélectionner le bon fichier de configuration",
      importProjectConfigException:
        "Exception lors de l'importation de la configuration du projet",
      exportPathNotExists: "Le chemin d'exportation n'existe pas",
      exportProjectConfigException:
        "Exception lors de l'exportation de la configuration du projet",
    },

    // Window service
    window: {
      initializeWindowState: "Initialiser la gestion de l'état de la fenêtre",
      startPeriodicSave:
        "Commencer la sauvegarde périodique de l'état de la fenêtre (toutes les 30 secondes)",
      windowStateInitialized: "Gestion de l'état de la fenêtre initialisée",
      windowStateInitFailed:
        "Échec de l'initialisation de la gestion de l'état de la fenêtre",
      debugAutoOpenDevTools:
        "Ouvrir automatiquement DevTools en mode debug, maintenant commenté",
      clickNotification: "Vous avez cliqué sur le message de notification",
      closeNotification: "Vous avez fermé le message de notification",
      directExit: "Sortie directe",
      minimizeToTray: "Réduire dans la barre d'état",
      dragWindow: "Faire glisser la fenêtre",
      saveCurrentWindowState: "Sauvegarder l'état actuel de la fenêtre",
      windowStateSaved: "État de la fenêtre sauvegardé manuellement",
      saveWindowStateFailed:
        "Échec de la sauvegarde manuelle de l'état de la fenêtre",
      windowStateRestored: "État de la fenêtre restauré manuellement",
      restoreWindowStateFailed:
        "Échec de la restauration manuelle de l'état de la fenêtre",
      checkProgramExists: "Vérifier si le programme existe",
    },

    // Tray service
    tray: {
      trayService: "Barre d'état",
    },

    // Report service
    report: {
      reportService: "Service lié aux rapports",
      getCommonReport: "Obtenir un rapport commun",
      getCommonReportEntry:
        "Journal d'entrée de la méthode d'obtention de rapport commun",
      errorHandledInCatch: "Erreur gérée dans catch",
      getGroupReport: "Obtenir un rapport de groupe",
      getOperateReport: "Obtenir un rapport d'opération",
      getAuditReport: "Obtenir un rapport d'audit",
      exportCommonReport: "Exporter un rapport commun",
      clearReport: "Effacer le rapport",
      refreshReport: "Actualiser le rapport",
      uploadWaveFile: "Télécharger un fichier d'onde",
      cancelUploadWaveFile: "Annuler le téléchargement du fichier d'onde",
      openWaveFile: "Ouvrir un fichier d'onde",
      passFilePathAsParam:
        "Passer le chemin du fichier comme paramètre à l'exe",
      generateExcelFile: "Générer un fichier Excel",
      tableHeader: "En-tête de tableau",
      exportContentFields: "Champs de contenu d'exportation",
    },
  },
};
